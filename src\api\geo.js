/* eslint-disable no-async-promise-executor */
import UA from "@/utils/ua"
import leadeonLoader from "@/utils/leadeonloader"
import ydsc from "./ydsc"
import gcoord from "gcoord"
export function getPosition(debug = false) {
  return new Promise(async resolve => {
    if(ydsc.checkApi()){
      ydsc.getAppLocationInfo().then(res=> {
        resolve(res)
      })
    } else if (UA.isApp) {
      // console.log(1)
      const leadeon = await leadeonLoader()
      leadeon.getLocation({
        debug,
        isShowLocationAlert: true,
        success: (data) => {
          console.log(data, 'leadeon.getLocation')
          const {
            latitude,
            longitude
          } = data
          const tranfromGeo = geoTransform({ latitude, longitude, from: gcoord.GCJ02, to: gcoord.WGS84 })
          resolve({
            code: 0,
            data: {
              ...data,
              ...tranfromGeo
            }
          })
        },
        error: error => {
          resolve({
            code: 1,
            msg: "获取位置信息失败",
            data: error
          })
        }
      })
    } else if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        ({ coords }) => {
          resolve({
            code: 0,
            data: coords
          })
        },
        error => {
          resolve({
            code: 1,
            msg: "获取位置信息失败",
            data: error
          })
        },
        {
          enableHighAccuracy: true,//高精度
          maximumAge: 60*1000, //缓存时间60秒
          timeout: 3000, //超时时间3秒
        }
      )
    }
  })
}
export function getLocation(debug = false){
  return new Promise(async resolve => {
    if (UA.isApp) {
      const leadeon = await leadeonLoader()
      leadeon.getUserInfo({
        debug,
        success(item) {
          // alert(888)
          let {province, city} = item
          province = parseInt(province)
          city = parseInt(city)
          if(city < 100) city = city*10
          resolve({
            code: 0,
            data: {
              province,
              city
            }
          })
        },
        error() {
          // alert(999)
          resolve({
            code: 1,
            data: {
              province: 100,
              city: 100
            }
          })
        }
      })
    }else {
      resolve({
        code: 1,
        data: {
          province: 100,
          city: 100
        }
      })
    }
  })
}
export function geoTransform({ latitude,longitude,  from = gcoord.GCJ02, to = gcoord.WGS84 }){
  if(latitude && longitude){
    let result = gcoord.transform(
      [longitude,latitude],    // 经纬度坐标[116.403988, 39.914266]
      from,
      to
    )
    return {
      latitude:result[1],
      longitude:result[0]
    }
  }
}
export default {
  getPosition,
  getLocation,
  geoTransform
}
