"use strict";
const TerserPlugin = require("terser-webpack-plugin");
const path = require("path");
const defaultSettings = require("./src/settings.js");

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = defaultSettings.title || "vue Admin Template"; // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following methods:
// port = 9527 npm run dev OR npm run dev --port = 9528
const port = process.env.port || process.env.npm_config_port || 9527; // dev port

const proxyServer = {
  ip: process.env.PROXY_IP,
  m: process.env.PROXY_M || "m.staff.ydsc.liuliangjia.cn",
  img1: process.env.PROXY_IMG1 || "img1.staff.ydsc.liuliangjia.cn",
};
const cacheGroups = {
  libs: {
    name: "libs",
    test: /[\\/]node_modules[\\/]/,
    priority: 10,
    chunks: "initial", // only package third parties that are initially dependent
  },
  elementui: {
    name: "elementui", // split elementUI into a single package
    priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
    test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
  },
  vantui: {
    name: "vantui", // split elementUI into a single package
    priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
    test: /[\\/]node_modules[\\/]_?vant(.*)/, // in order to adapt to cnpm
  },
};

const chunks = ["runtime", ...Object.keys(cacheGroups)];

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */

  publicPath: "/yundian/",
  outputDir: "dist/yundian/",
  assetsDir: "static",
  lintOnSave: process.env.NODE_ENV === "development",
  parallel: !(process.platform === 'win32' && process.env.NODE_ENV === "production"),
  productionSourceMap: false,
  devServer: {
    port: port,
    open: false,
    allowedHosts: ["*"],
    // 代理，原来通配'**'改为''
    proxy: {
      "/fs_server/": {
        target: "https://" + proxyServer.img1,
        changeOrigin: false,
        headers: {
          host: proxyServer.img1,
          referer: "https://" + proxyServer.img1,
        },
        pathRewrite: {
          "/fs_server/": "/",
        },
      },
      "/yundian/netShop/": {
        target: proxyServer.ip,
        // target: "http://**********:9099",//ZUMING IP
        changeOrigin: false,
        headers: {
          host: proxyServer.m,
          referer: "https://" + proxyServer.m,
        },
      },
      "/ajax/": {
        target: proxyServer.ip,
        changeOrigin: false,
        headers: {
          host: proxyServer.m,
          referer: "https://" + proxyServer.m,
        },
      },
    },
    //before: require('./mock/mock-server.js')
  },
  configureWebpack: {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: name,
    resolve: {
      alias: {
        "@": resolve("src"),
      },
    },
    devtool: "source-map",
    //删除注释
    cache: {
      type: "filesystem",
    },
    optimization: {
      runtimeChunk: {
        name: "runtime",
      },
      minimize: true,
      minimizer: [
        // new CssMinimizerPlugin(),
        new TerserPlugin({
          terserOptions: {
            format: {
              comments: false,
            },
            sourceMap: process.env.NODE_ENV !== "production",
            output: {
              comments: false,
            },
          },
          extractComments: false,
        }),
      ],
      // splitChunks: {
      //   chunks: "all",
      // },
    },
  },
  // css: {
  //     loaderOptions: {
  //         postcss: {
  //         }
  //     }
  // },
  chainWebpack(config) {
    config.plugins.delete("preload"); // TODO: need test
    config.plugins.delete("prefetch"); // TODO: need test
    const keys = config.plugins.store.keys();
    let key = keys.next().value;
    while (key) {
      if (key.indexOf("preload") == 0 || key.indexOf("prefetch") == 0) {
        config.plugins.delete(key);
      }
      if (key.indexOf("html-") == 0) {
        //修改多入口chunks
        config.plugin(key).tap((args) => {
          const page = args[0];
          page.chunks = [...chunks, page.chunks.pop()];
          args[0] = page;
          return args;
        });
      }
      key = keys.next().value;
    }
    // set svg-sprite-loader
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      })
      .end();

    // set preserveWhitespace
    config.module
      .rule("vue")
      .use("vue-loader")
      .loader("vue-loader")
      .tap((options) => {
        options.compilerOptions.preserveWhitespace = true;
        return options;
      })
      .end();

    config.when(process.env.NODE_ENV !== "development", (config) => {
      // config
      //   .plugin('ScriptExtHtmlWebpackPlugin')
      //   .after('html')
      //   .use('script-ext-html-webpack-plugin', [{
      //     // `runtime` must same as runtimeChunk name. default is `runtime`
      //     inline: /runtime\..*\.js$/
      //   }])
      //   .end()
      config.optimization.splitChunks({
        chunks: "all",
        cacheGroups: cacheGroups,
      });
      config.optimization.runtimeChunk("single");
      // config.optimization.runtimeChunk({
      //   name: entryPoint => `manifest.${entryPoint.name}`
      // })
    });
  },
};
