# 卓望手机端模板工程

* [线上地址]()
* [本地地址] http://localhost:8080

***

## 技术栈

* vue
* vue-router
* axios
* 暂无集成UI框架,根据情况集成vant-ui
* vant-ui(https://youzan.github.io/vant/#/zh-CN/intro)
* scss
* es6+
* amfe-flexible
* postcss-loader
* 检查更新工具
* npm-check-updates[可选](https://www.npmjs.com/package/npm-check-updates)

## 目录

```
├── build                       // 构建相关
├── config                      // 配置相关
├── doc                        // 文档库
├── src                         // 源代码
│   ├── api                    // 所有请求
│   ├── assets                 // 主题 字体等静态资源
│   ├── components             // 全局公用组件
│   ├── directive              // 全局指令
│   ├── filters                // 全局 filter
│   ├── router                 // 路由
│   ├── store                  // 全局 store管理
│   ├── styles                 // 全局样式
│   ├── utils                  // 全局公用方法
│   ├── views                  // view
│   ├── App.vue                // 入口页面
│   └── main.js                // 入口 加载组件 初始化等
├── static                      // 第三方不打包资源
├── .babelrc                    // babel-loader 配置
├── eslintrc.js                 // eslint 配置项
├── .gitignore                  // git 忽略项
├── .npmrc                      // 卓望私有npm库配置
├── favicon.ico                 // favicon图标
├── index.html                  // html模板
└── package.json                // package.json
```

## 项目文档

1. 需求文档，原型 以及 UI 规范见 `doc` 目录
2. [后端 API]()

## 项目说明
``` bash
# 项目首页svn地址

# webpack 反向代理配置

```

## 规范

#### IDE推荐

* IDE建议使用 `vscode`或`webstorm` 编辑器
* 使用 `ESlint` 做代码检查，规则见 `.eslintrc.js` 文件
* `webstorm` eslint 自动格式化设置参考 [链接](http://www.bubuko.com/infodetail-2454899.html?tdsourcetag=s_pctim_aiomsg)

***

#### 编码规范

* 样式使用 [BEM](http://211.139.191.230:12630/android/fe/pages/standard/css-name.html) 命名规范

***

#### JS&VUE

* js请统一使用 es6+ 语法
* 使用全等符号 '===' 作值判断 替代 '=='
* 关于不确定的变量，请做好 空值/类型 的判断，建议多引入异常捕获
*  [vue 风格规范](https://cn.vuejs.org/v2/style-guide/)

***

#### 其他规范

* 文件，变量的命名采用驼峰命名，私有变量加下划线前缀区分 ex: let _demoData = data
* 勿使用中文拼音，严格杜绝拼写错误
* 图片使用中横线命名，图片前加模块前缀，ex：bg-demo.png  icon-delete.png

***

#### git管理规范

dev为开发分支，dev-[name] 对应开发人员私有分支，建议在各自分支开发

代码提交前请务必确保冲突已解决，确保无任何编译error（包括eslint校验提示）

## 移动端自适应配置

``` bash
# 基于amfe-flexible和postcss-loader

# build/vue-loader.conf.js配置，添加

postcss: [
    require('autoprefixer')({
        browsers: ['last 2 versions']
    }),
    require('postcss-plugin-px2rem')({
        rootValue: 75, // 设计稿宽度除以10
        unitPrecision: 8,
        propWhiteList: [],
        propBlackList: [],
        selectorBlackList: [],
        ignoreIdentifier: false,
        replace: true,
        mediaQuery: false,
        minPixelValue: 2
    })
]

```

## 打包配置

#### 配置config/index.js

``` bash
# 不生成map文件
* productionSourceMap: false
# 开启gzip 压缩
# 安装：npm install compression-webpack-plugin --save-dev
* 注意安装后"compression-webpack-plugin": "^2.0.0",需要降低版本为"compression-webpack-plugin": "^1.1.12",重新运行npm install
* 开启：productionGzip: true

# 局域网内，方便在手机端，动态获取IP地址：
# host:getIPAddress(), // host:'0.0.0.0',动态获取当前机器IP地址

```

#### 配置build/utils.js

`` bash
if (options.extract) {
    return ExtractTextPlugin.extract({
        use: loaders,
        fallback: 'vue-style-loader',
        publicPath: '../../' // css读取背景图再也不需要更改路径了
    })
} else {
    return ['vue-style-loader'].concat(loaders)
}
``

## 构建

```bash
# 克隆项目
vue-init direct:http://gitlab.shop.staff.139.com/vuetemplate/aspire-vue-element-template/repository/archive.zip asp-template-project

# 进入项目目录
cd template-project

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装以来，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npm.taobao.org

# 启动服务
npm run dev
```

浏览器访问 [http://localhost:9528](http://localhost:9528)

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build
```

## 其它

```bash
# 预览发布环境效果
npm run preview

# 预览发布环境效果 + 静态资源分析
npm run preview -- --report

# 代码格式检查
npm run lint

# 代码格式检查并自动修复
npm run lint -- --fix
```

## 需要修复的问题
1. adduid, 只有从导航链接取artifact的时候才调用addyuid

## 王蕊说的附近店铺推广页标签问题 (已改)
fixbug_20230202_tag_1

## 中国移动app打不开首页的问题 (只上了灰度, 现网环境不上也没关系)
fixbug_20230201_applogin_1

## 未知分支 (笔记里面记录了某些分支, 但具体改了什么不清楚, 在这里做下记录)
pagedataindicators

***

## 合约购机
1. 金币购机-本地-灰度
http://localhost:9527/yundian/preferentialpurchase/index.html?requestParam=t65kAZqIeAnu6OKVvWVc6IslSYy4WSu6q5tHoCX+%2FmE%3D++++++++&serialNo=166859768222821293&shareParam=c3RvcmVJZD00NzEwMDAyNzI4OSZnb29kc0lkPTM0NjAyMSZwaG9uZU51bT0xODM2MDQ1NzEwOCZvdGhlclN0b3JlSWQ9&upgradeType=3&shopId=10000182

2. 金币购机-灰度
https://grey.touch.10086.cn/yundian/preferentialpurchase/index.html?requestParam=t65kAZqIeAnu6OKVvWVc6IslSYy4WSu6q5tHoCX+%2FmE%3D++++++++&serialNo=166859768222821293&shareParam=c3RvcmVJZD00NzEwMDAyNzI4OSZnb29kc0lkPTM0NjAyMSZwaG9uZU51bT0xODM2MDQ1NzEwOCZvdGhlclN0b3JlSWQ9&upgradeType=3&shopId=10000182

3. 金币购机-线上
https://touch.10086.cn/yundian/preferentialpurchase/index.html?requestParam=t65kAZqIeAnu6OKVvWVc6Hw7xksIz1SRNbqEwaD2LFQ%3D++++++++&serialNo=168437561339614365&shareParam=c3RvcmVJZD00NzEwMDAzMzc4MCZnb29kc0lkPTE2NzI2NjYmcGhvbmVOdW09MTU4NDgzOTQ2OTUmb3RoZXJTdG9yZUlkPTM2MjM%3D&upgradeType=3&shopId=3623

### 测试手机号
蕊姐手机号 : 13811627221
崔叔胺手机号 : 18810770328
永杰301 : 15901479002      15010347550
 史同德 :13693648062
18889086684


### 号卡订单列表链接
http://localhost:9527/yundian/order/list.html?shopId=10000182
https://grey.touch.10086.cn/yundian/index.html?shopId=10000182  内蒙店铺188（18889086684西藏-拉萨），灰度内蒙这里数据

### 杨帆设计图地址
https://app.mockplus.cn/s/FSX7EJyRbRzz
<EMAIL>
y49782311f

### 触屏商城灰度jk
http://jenkins.grey.ydsc.aspire-tech.com/login?from=%2F
jenkins
6911DbF07aB9

### 雪峰设计图地址
https://app.mockplus.cn/s/iGNouiyqtbGg
<EMAIL>
密码mock01@dxf

### 填写周报地址
https://docs.qq.com/sheet/DU3ZzZG9sZ0VURHRz?tab=BB08J2

### 填写bug地址
https://docs.qq.com/sheet/DQWR1d2FhQ3ZIc1pu?tab=iereeh

### 代码review
http://***********:9000/dashboard?id=MobileMallNetShopPC
zhangmiao
202106@shawnc

### 云店上线灰度pass (日期:2023/05/22)
地址: http://paas.ydsc.cn/
pass平台写hosts
************ img0.zz.ydsc.liuliangjia.cn img1.zz.ydsc.liuliangjia.cn
************ paas.ydsc.cn kibana.ydsc  kibana.ydsc jkzz.ydsc jenkins.ydsc
************ paas.ydsc.cn
shoppingmall-admin
1qaz!QAZ!@#$%^&*

### 商城测模拟登录
http://m.staff.ydsc.liuliangjia.cn/sso/autoinituser.php?key=14d3a4108a2e4244a1f9247ee0a66beb&user_name=15010347550&province=100&brand=01&auth_user_id=15010347550&user_type=01
点击店铺管理即可

### 商城跳云店， 灰度登录信息传不过来， 需要把cookie信息的is_login 重新设置为true

### 云店有企业微信功能的店铺
https://touch.10086.cn/yundian/index.html?shopId=7
北京范瑞宏 有异业 有企微

### whistle抓包1
npm install -g whistle
w2 start -p 8081
w2 stop

### 想要加密的数据, axios的config加needEncrypt:1, 参考api/broadband.js的getUserBAOrderList方法

### 爆款潮品不显示
原因 : 接口还在pending, 并且用户已经切换到爆款潮品, 切换后数据才请求到, 这时候页面没有渲染新的数据
解决方法 : 获取到数据后强制渲染, this.$forceUpdate()