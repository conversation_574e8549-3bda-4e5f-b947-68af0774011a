// 登录逻辑
import { getQueryString } from "@/utils/utils"
import UA from "../ua"
import H5Login from "./H5Login"
import AppLogin from "./appLogin"
import wachatWorkLoginApi from "./wechatWorkLogin"
import { getToken, removeLocalStorage } from "./localStorage"
import { getUserInfoByArtifact } from "./getUserInfo"
import Cookie from "js-cookie"
import { tySSO } from "./tysso"
import APPSSO from "./appsso"
import store from "../../store"
import loginApi from "@/api/login"
/**
 * @param {Boolean} h5ForceLogin h5是否强制登录
 * @param {Boolean} appForceLogin app是否强制登录
 * @param {Function} sucCb 登录成功后的回调
 * @param {Boolean} smsLogin h5是否使用弹窗短信验证码登录
 * @param {Boolean} autoLogin 是否支持4G免登
 * @param {Boolean} autoLoginCb 4G免登回调
 * @param {Boolean} userType 4G免登回调
 * @param {Boolean} reqsource 1是获取触屏首页店铺缓存中的shopid以及用户信息，5是直接根据shopId获取用户信息
 */
let auth = null
function login(
  h5ForceLogin = false,
  appForceLogin = false,
  sucCb = () => {},
  smsLogin = false,
  autoLogin = false,
  autoLoginCb =()=>{},
  userType=null,
  reqsource=null
){
  loginAfter({
    h5ForceLogin,
    appForceLogin,
    sucCb,
    smsLogin,
    autoLogin,
    autoLoginCb,
    userType,
    reqsource
  })
}
async function loginAfter({
  h5ForceLogin = false,
  appForceLogin = false,
  sucCb = () => {},
  smsLogin = false,
  autoLogin = false,
  autoLoginCb =()=>{},
  userType=null,
  reqsource=null
}) {
  if(UA.isWechatWork){
  // if(!UA.isWechatWork){ //调试
    wechatWorkLogin({backUrl:location.href,forceLogin:h5ForceLogin,reqsource,sucCb})
    return false
  }
  const channelID = "12118",
    testLoginUrl = "https://actest.10086.cn", // 测试登录域名
    prodLoginUrl = "https://login.10086.cn", // 生产登录域名
    isProd = location.host.indexOf("staff.ydsc.liuliangjia.cn") == -1 // 是否为生产地址
  let dq_uid = localStorage.getItem("dq_uid"),
    loginUrl = isProd ? prodLoginUrl : testLoginUrl,
    isLogined
  if(getToken()){
    auth = await loginApi.getUserInfo(getToken(),userType,reqsource).then((res)=>{
      if (res.code == 0) {
        store.commit("SET_USERINFO", res.data)
        // console.log(77777)
        // store.commit('SET_LOGINFIRST',1)
        return res.data
      } else if (res.code == 1) {
        removeLocalStorage()
        return null
      }
    })
    isLogined = auth && auth.UserName // 用户是否登录过期
  } else {
    isLogined = false
  }
  console.log('UA.isApp 1')
  if (UA.isApp) {
    AppLogin(appForceLogin, loginUrl, channelID, sucCb, isLogined,userType,reqsource,auth)
  } else {
    // 通过链接上artifact登录
    let artifact = getQueryString("artifact")
    let uid = getQueryString("uid")
    let token = getQueryString("cmToken")
    let is_login = Cookie.get("is_login")
    let status = 1 // 0 成功 1 失败
    if (UA.isWechat && window.wx && window.wx.miniProgram && !UA.isWechatWork) {
      //排除企业微信，企业微信按正常页面走
      const isWXMapp = await UA.isWeChatMiniApp()
      if (isWXMapp) {
        is_login = null
        // 小程序内
        if(artifact){
          //清空登录
          localStorage.clear()
          isLogined = false
        }else if(uid == ""){
          localStorage.clear()
          isLogined = false
        } else if (
          (dq_uid && uid != null && dq_uid == uid && isLogined) ||
          (uid == null && isLogined) //跳到下一页时链接没有uid
        ) {

          sucCb(auth)
          delParams()
          return
        }
      } else {
        // 微信内
        if (isLogined) {
          sucCb(auth)
          delParams()
          return
        }
      }
    } else {
      if (!artifact && isLogined) {
        sucCb(auth)
        delParams()
        return
      }
    }

    // artifact登录
    if (artifact) {
      let artifactResult = await getUserInfoByArtifact(
        artifact,
        delParamsAndInit(sucCb),
        delParams,
        userType,
        null,
        reqsource
      )
      status = artifactResult.status
      if (!status) {
        //登录成功后就不往下走了
        return
      }
    }

    // 大网token登录
    if (token) {
      let artifactResult = await getUserInfoByArtifact(
        null,
        delParamsAndInit(sucCb),
        delParams,
        userType,
        token,
        reqsource
      )
      status = artifactResult.status

      //登录成功后就不往下走了
      if (!status) {
        return
      }
    }
    // uid登录
    if (status && uid) {
      localStorage.setItem("dq_uid", uid)
      APPSSO(loginUrl, channelID, uid, sucCb,delParams,userType,null,reqsource) //失败没有回调
    } else if (status && is_login) { //islogin只有10086域名下才能查到true，灰度环境不可以，but可以10086域名下模拟true带进登录信息
      tySSO(async(artifact) => {
        if (artifact && artifact != -1) {
          let tyResult = await getUserInfoByArtifact(
            artifact,
            delParamsAndInit(sucCb),
            delParams,
            userType,
            null,
            reqsource
          )
          status = tyResult.status
        } else {
          // 未登录将不再传-1
          Cookie.remove("is_login", { path: "/", domain: ".10086.cn" })
          Cookie.remove("is_login", { path: "/" })
          if (autoLogin) {
            autoLoginCb(h5ForceLogin, appForceLogin,smsLogin)
          } else if (h5ForceLogin) {
            H5Login(loginUrl, channelID, smsLogin)
          } else {
            sucCb()
            return 2
          }
        }
      })
    } else if (autoLogin) {
      //console.log(66666)
      autoLoginCb(h5ForceLogin, appForceLogin,smsLogin)
    } else if (status && h5ForceLogin) {
      H5Login(loginUrl, channelID, smsLogin)
      return 2
    } else {
      !h5ForceLogin && sucCb(auth) //回调加上返回值
      return 2
    }
  }
}
async function wechatWorkLogin({
  backUrl,
  forceLogin = false,
  userType=null,
  reqsource=null,
  sucCb = () => {},
}){
  /** 判断当前是否是登录状态 */
  let isLogined = false
  if(getToken()){
    auth = await loginApi.getUserInfo(getToken(),userType,reqsource).then((res)=>{
      if (res.code == 0) {
        store.commit("SET_USERINFO", res.data)
        // console.log(77777)
        // store.commit('SET_LOGINFIRST',1)
        return res.data
      } else if (res.code == 1) {
        removeLocalStorage()
        return null
      }
    })
    isLogined = auth && auth.UserName // 用户是否登录过期
  } else {
    isLogined = false
  }
  if (isLogined) {
    sucCb(auth)
    delParams()
    return
  } else {
    /** 未登录状态先查看是不是企业微信登录成功回调页面 */
    let code = getQueryString("code")
    if(code){
      let userInfoRes = await wachatWorkLoginApi.getUserInfoByWechatCode(
        code,
        delQwParamsAndInit(sucCb),
        delQwParams,
        userType,
        reqsource
      )
      // {
      //   "code": 0,
      //   "message": "ok",
      //   "additions": null,
      //   "status": 200,
      //   "data": {
      //     "userCity": "",
      //     "AuthUserID": "19804710660",
      //     "UserName": "19804710660",
      //     "userProvince": "",
      //     "wtmobile": "55056-58976-6569-32841",
      //     "isCMO": null,
      //     "RuleId": "0",
      //     "token": "109f3d3885be31515a7229fd1f0c8e15"
      //   }
      // }
      delQwParams()
      if (!userInfoRes.status) { // 0 成功 1 失败
        //登录成功后就不往下走了
        return
      }
    }
    /** 未登录状态且不是企业微信登录成功回调页面则跳转企微登录页 */
    if(forceLogin) {
      wachatWorkLoginApi.getWechatCode(backUrl)
      return false
    }
    sucCb()
    return 2
  }

}

// 删除链接上的artifact、type、uid
const delParams = () => {
  let url = new URL(location.href),
    params = url.searchParams
  params.delete("artifact")
  params.delete("type")
  params.delete("uid")
  params.delete("cmToken")
  history.replaceState(null, null, url.toString())
}

// 删除链接上的参数同时执行初始化方法
const delParamsAndInit = (sucCb) => {
  return (opts) => {
    delParams()
    sucCb && sucCb(opts)
  }
}
// 删除企微环境下链接上的code
const delQwParams = () => {
  let url = new URL(location.href),
    params = url.searchParams
  params.delete("code")
  history.replaceState(null, null, url.toString())
}

// 删除链接上的参数同时执行初始化方法
const delQwParamsAndInit = (sucCb) => {
  return (opts) => {
    delQwParams()
    sucCb && sucCb(opts)
  }
}

export default {
  login,
  loginAfter,
  wechatWorkLogin
}
